<template>
</template>
<script>
import fileDialogService from '@/utils/fileDialogService';
export default {
  name: '',
  components: {},
  props: {},
  data () {
    return {}
  },
  mounted () {},
  methods: {
    initData(value){
      // 使用新的文件对话服务
      try {
        const files = value.map(v => v.userDocName);
        const docIds = value.map(v => v.docId).join(",");

        // 设置状态组件引用
        fileDialogService.setStatusComponent(this);

        // 启动文件对话
        fileDialogService.startFileDialog(files, docIds);
      } catch (error) {
        console.error('启动文件对话失败:', error);
        this.$message.error(error.message || '启动文件对话失败');
      }
    },
    // 更新队列信息（fileDialogService 需要的方法）
    updateQueueInfo(queueInfo) {
      // 这里可以处理队列状态更新，比如显示进度
      console.log('队列状态更新:', queueInfo);
    },


  }
}
</script>

<style scoped lang="scss">
</style>
