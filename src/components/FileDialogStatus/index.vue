<template>
  <div v-if="hasAnyTask" class="file-dialog-queue">
    <div class="queue-header">
      <span class="title">文件对话队列 ({{ queueInfo.queue.length }})</span>
      <i class="el-icon-close close-btn" @click="clearQueue"></i>
    </div>

    <!-- 统一队列列表 -->
    <div class="queue-list">
      <div class="queue-scroll">
        <div
          v-for="(item, index) in queueInfo.queue"
          :key="item.id"
          class="task-item"
          :class="`status-${item.status}`"
        >
          <div class="queue-number">{{ index + 1 }}</div>
          <el-tooltip
            :content="formatAllFileNames(item.files)"
            placement="left"
          >
            <div class="file-name">{{ formatSingleFileName(item.files) }}</div>
          </el-tooltip>

          <div class="task-status">
            <!-- 处理中 -->
            <span v-if="item.status === 'processing'" class="processing">
              <i class="el-icon-loading"></i>
              创建中...
            </span>
            <!-- 等待中 -->
            <span v-else-if="item.status === 'waiting'" class="waiting-text">等待中</span>
            <!-- 已就绪 -->
            <el-button
              v-else-if="item.status === 'ready'"
              type="primary"
              size="mini"
              @click="openTaskChat(item.id)"
            >
              打开
            </el-button>
            <!-- 失败 -->
            <el-tooltip v-else-if="item.status === 'failed'" :content="item.error" placement="top">
              <span class="failed-text">
                <i class="el-icon-error"></i>
                失败
              </span>
            </el-tooltip>
          </div>

          <i
            v-if="item.status !== 'processing'"
            class="el-icon-delete remove-btn"
            @click="removeTask(item.id)"
          ></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import fileDialogService from '@/utils/fileDialogService'

export default {
  name: 'FileDialogStatus',
  data() {
    return {
      queueInfo: {
        queue: []
      }
    }
  },

  computed: {
    hasAnyTask() {
      return this.queueInfo.queue && this.queueInfo.queue.length > 0
    }
  },

  created() {
    // 在组件创建时设置引用
    fileDialogService.setStatusComponent(this)
  },

  beforeDestroy() {
    // 在组件销毁时清除引用
    fileDialogService.setStatusComponent(null)
  },

  methods: {
    // 更新队列信息（由 fileDialogService 调用）
    updateQueueInfo(queueInfo) {
      this.queueInfo = queueInfo
    },

    // 打开就绪任务的Chat
    openTaskChat(taskId) {
      fileDialogService.openTaskChat(taskId)
    },

    // 移除单个任务
    removeTask(taskId) {
      fileDialogService.removeTask(taskId)
    },

    // 清空等待队列
    clearQueue() {
      fileDialogService.clearQueue()
    },

    // 显示第一个文件名
    formatSingleFileName(files) {
      return files?.[0] || '未知文件'
    },

    // tooltip显示全部文件名
    formatAllFileNames(files) {
      return files?.join('\n') || '未知文件'
    }
  }
}
</script>

<style lang="scss" scoped>
.file-dialog-queue {
  position: fixed;
  top: 100px;
  right: 4px;
  width: 220px;
  max-height: 400px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  z-index: 20;
  display: flex;
  flex-direction: column;

  .queue-header {
    padding: 8px 16px;
    border-bottom: 1px solid #e4e7ed;
    border-radius: 6px 6px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .close-btn {
      cursor: pointer;
      color: #909399;
      font-size: 16px;

      &:hover {
        color: #606266;
      }
    }
  }

  .queue-list {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .queue-scroll {
      flex: 1;
      overflow-y: auto;
      max-height: 200px;
    }
  }

  .task-item {
    padding: 8px 16px;
    border-bottom: 1px solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }

    display: flex;
    align-items: center;
    gap: 8px;

    &.status-processing {
      background: #f0f9ff;
      border-left: 3px solid #409eff;
      padding-left: 13px;
    }

    &.status-failed {
      background-color: #fef0f0;
    }

    .queue-number {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #e4e7ed;
      color: #909399;
      font-size: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .file-name {
      font-size: 13px;
      color: #606266;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      line-height: 1.4;
      flex: 1;

      &:hover {
        color: #409eff;
      }
    }

    .task-status {
      .processing {
        font-size: 12px;
        color: #409eff;
        display: flex;
        align-items: center;

        i {
          margin-right: 4px;
        }
      }

      .waiting-text {
        font-size: 11px;
        color: #c0c4cc;
      }

      .failed-text {
        font-size: 12px;
        color: #f56c6c;
        display: flex;
        align-items: center;
        cursor: pointer;
        i {
          margin-right: 4px;
        }
      }
    }

    .remove-btn {
      cursor: pointer;
      color: #909399;
      font-size: 14px;
      margin-left: auto;
      padding: 4px;
      border-radius: 50%;
      &:hover {
        background-color: #f5f7fa;
        color: #f56c6c;
      }
    }
  }
}
</style>
