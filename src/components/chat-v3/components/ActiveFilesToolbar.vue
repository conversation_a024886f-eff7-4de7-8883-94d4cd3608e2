<template>
  <div class="active-files-toolbar">
    <div class="files-list">
      <div v-for="(fileName, index) in activeFiles" :key="index" class="file-tag">
        <img :src="getFileIcon(fileName)" alt="file" class="file-icon"/>
        <span class="file-name" :title="fileName">{{ fileName }}</span>
        <!-- 只有一个文件时显示总结文档按钮 -->
        <el-button
          v-if="activeFiles.length === 1 && index === 0"
          type="text"
          size="mini"
          class="summary-btn"
          @click="handleSummaryDocument"
          :loading="summaryLoading"
        >
          总结文档
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import knowledgeApi from '@/api/knowledge.js'

export default {
  name: 'ActiveFilesToolbar',
  data() {
    return {
      summaryLoading: false
    }
  },
  computed: {
    ...mapState('chat-v3', ['activeFiles', 'chatSettings', 'currentKnowledge']),
    ...mapGetters('chat-v3', ['currentKnowledgeName'])
  },
  methods: {
    getFileIcon(fileName) {
      if (!fileName) return require('../icons/file-types/default.svg')

      // 确保fileName是字符串
      const fileNameStr = typeof fileName === 'string' ? fileName : String(fileName)

      const ext = fileNameStr.split('.').pop().toLowerCase()
      try {
        return require(`../icons/file-types/${ext}.svg`)
      } catch (e) {
        return require('../icons/file-types/default.svg')
      }
    },

   async handleSummaryDocument() {
     if (this.activeFiles.length !== 1) return;

     const fileName = typeof this.activeFiles[0] === 'string' ? this.activeFiles[0] : String(this.activeFiles[0]);

     // 1. 添加用户和AI占位消息
     this.$store.commit('chat-v3/ADD_MESSAGE', {
       type: 'user',
       answer: `总结文档: ${fileName}`,
       timestamp: Date.now()
     });

     const aiPlaceholderMessage = {
       type: 'assistant',
       answer: '', // 关键点：answer为空
       done: false, // 关键点：done为false
       timestamp: Date.now()
     };
     this.$store.commit('chat-v3/ADD_MESSAGE', aiPlaceholderMessage);

     // 获取刚刚添加的占位消息的ID
     const aiMessageId = this.$store.state['chat-v3'].messages[this.$store.state['chat-v3'].messages.length - 1].id;

     // 2. 设置加载状态
     this.$store.commit('chat-v3/SET_SENDING', true);
     this.summaryLoading = true;

     let aiFinalMessage = {};

     try {
       const knowledgeName = this.currentKnowledge?.label || this.currentKnowledgeName;
       const modelName = this.chatSettings?.model_name;
       const temperature = this.chatSettings?.temperature;

       if (!fileName || !knowledgeName || !modelName) {
         throw new Error('缺少必要的配置参数，请重试');
       }

       // 3. 调用API
       const response = await knowledgeApi.sendBigFileQuestion(
         JSON.stringify({
           knowledge_base_name: knowledgeName,
           file_name: fileName,
           allow_empty_kb: true,
           vs_type: "faiss",
           embed_model: "bge-large-zh-v1.5",
           file_description: "",
           model_name: modelName,
           temperature: temperature,
         })
       );

       let startIndex = response.indexOf("data:") + "data:".length;
       let jsonString = response.substring(startIndex).trim();
       let result = JSON.parse(jsonString);

       if (result.code === 200) {
         aiFinalMessage = {
           answer: result.data,
           done: true,
           error: false
         };
       } else {
         throw new Error(result.msg || '文档总结失败');
       }
     } catch (error) {
       console.error('总结文档失败:', error);
       this.$message.error(error.message || '文档总结失败，请重试');
       aiFinalMessage = {
         answer: `抱歉，文档总结失败: ${error.message}`,
         done: true,
         error: true
       };
     } finally {
       // 4. 更新AI消息
       this.$store.commit('chat-v3/UPDATE_MESSAGE', {
         id: aiMessageId,
         updates: aiFinalMessage
       });

       // 5. 恢复加载状态
       this.$store.commit('chat-v3/SET_SENDING', false);
       this.summaryLoading = false;
     }
   }
  }
}
</script>

<style lang="scss" scoped>
.active-files-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  background-color: transparent;
  border-radius: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.files-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.file-tag {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 15px;
  padding: 4px 10px;
  font-size: 12px;
  color: #333;
}

.file-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.summary-btn {
  margin-left: 8px;
  padding: 2px 6px;
  font-size: 11px;
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }

  &.is-loading {
    color: #c0c4cc;
  }
}
</style>
